package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"solve_api/internal/config"

	"github.com/redis/go-redis/v9"
	_ "github.com/go-sql-driver/mysql"
)

func main() {
	fmt.Println("=== 数据检查工具 ===")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}

	// 2. 检查Redis
	fmt.Println("\n--- 检查Redis ---")
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.GetRedisAddr(),
		Username: cfg.Redis.Username,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	defer rdb.Close()

	ctx := context.Background()
	
	// 测试连接
	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
	} else {
		fmt.Printf("✅ Redis连接成功: %s\n", pong)
		
		// 获取键数量
		dbSize, err := rdb.DBSize(ctx).Result()
		if err != nil {
			fmt.Printf("❌ 获取Redis数据库大小失败: %v\n", err)
		} else {
			fmt.Printf("📊 Redis键数量: %d\n", dbSize)
			
			if dbSize > 0 {
				// 显示部分键名
				keys, err := rdb.Keys(ctx, "*").Result()
				if err != nil {
					fmt.Printf("❌ 获取键列表失败: %v\n", err)
				} else {
					fmt.Printf("🔑 键名示例:\n")
					showCount := 5
					if len(keys) < showCount {
						showCount = len(keys)
					}
					for i := 0; i < showCount; i++ {
						fmt.Printf("   %d. %s\n", i+1, keys[i])
					}
				}
			}
		}
	}

	// 3. 检查MySQL
	fmt.Println("\n--- 检查MySQL ---")
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
		cfg.MySQL.Charset,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		fmt.Printf("❌ MySQL连接失败: %v\n", err)
		return
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		fmt.Printf("❌ MySQL ping失败: %v\n", err)
		return
	}
	fmt.Printf("✅ MySQL连接成功\n")

	// 检查questions表数据量
	var questionCount int
	err = db.QueryRow("SELECT COUNT(*) FROM questions WHERE deleted_at IS NULL").Scan(&questionCount)
	if err != nil {
		fmt.Printf("❌ 查询questions表失败: %v\n", err)
	} else {
		fmt.Printf("📊 Questions表记录数: %d\n", questionCount)
		
		if questionCount > 0 {
			// 显示最新的几条记录
			rows, err := db.Query("SELECT id, question_type, LEFT(question_text, 50) as question_preview, created_at FROM questions WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT 5")
			if err != nil {
				fmt.Printf("❌ 查询最新记录失败: %v\n", err)
			} else {
				fmt.Printf("📝 最新记录:\n")
				for rows.Next() {
					var id int
					var questionType, questionPreview, createdAt string
					if err := rows.Scan(&id, &questionType, &questionPreview, &createdAt); err != nil {
						continue
					}
					fmt.Printf("   ID:%d [%s] %s... (%s)\n", id, questionType, questionPreview, createdAt)
				}
				rows.Close()
			}
		}
	}

	// 4. 总结
	fmt.Println("\n--- 数据状态总结 ---")
	if dbSize == 0 && questionCount == 0 {
		fmt.Println("✅ Redis和MySQL都没有数据，可以进行全新测试")
	} else if dbSize > 0 || questionCount > 0 {
		fmt.Printf("⚠️  存在数据: Redis(%d键), MySQL(%d记录)\n", dbSize, questionCount)
		fmt.Println("建议清空数据后再进行测试")
	}
}
