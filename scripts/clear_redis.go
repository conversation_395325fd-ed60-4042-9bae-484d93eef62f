package main

import (
	"context"
	"fmt"
	"log"
	"solve_api/internal/config"
	"time"

	"github.com/redis/go-redis/v9"
)

func main() {
	fmt.Println("=== Redis清空工具 ===")

	// 1. 加载配置
	fmt.Println("\n--- 加载配置 ---")
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}
	fmt.Printf("✅ 配置加载成功\n")
	fmt.Printf("   Redis地址: %s\n", cfg.Redis.GetRedisAddr())
	fmt.Printf("   Redis用户: %s\n", cfg.Redis.Username)
	fmt.Printf("   Redis数据库: %d\n", cfg.Redis.DB)

	// 2. 连接Redis
	fmt.Println("\n--- 连接Redis ---")
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.GetRedisAddr(),
		Username: cfg.Redis.Username,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})
	defer rdb.Close()

	ctx := context.Background()

	// 测试连接
	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("❌ Redis连接失败: %v", err)
	}
	fmt.Printf("✅ Redis连接成功: %s\n", pong)

	// 3. 获取当前数据统计
	fmt.Println("\n--- 当前数据统计 ---")
	dbSize, err := rdb.DBSize(ctx).Result()
	if err != nil {
		log.Printf("❌ 获取数据库大小失败: %v", err)
	} else {
		fmt.Printf("   当前键数量: %d\n", dbSize)
	}

	// 获取内存使用情况
	_, err = rdb.Info(ctx, "memory").Result()
	if err != nil {
		log.Printf("❌ 获取内存信息失败: %v", err)
	} else {
		fmt.Printf("   内存信息获取成功\n")
	}

	// 4. 显示部分键名（用于确认）
	if dbSize > 0 {
		fmt.Println("\n--- 部分键名预览 ---")
		keys, err := rdb.Keys(ctx, "*").Result()
		if err != nil {
			log.Printf("❌ 获取键列表失败: %v", err)
		} else {
			showCount := 10
			if len(keys) < showCount {
				showCount = len(keys)
			}
			for i := 0; i < showCount; i++ {
				fmt.Printf("   %d. %s\n", i+1, keys[i])
			}
			if len(keys) > showCount {
				fmt.Printf("   ... 还有 %d 个键\n", len(keys)-showCount)
			}
		}
	}

	// 5. 确认清空操作
	if dbSize == 0 {
		fmt.Println("\n✅ Redis数据库已经是空的，无需清空")
		return
	}

	fmt.Println("\n--- 确认清空操作 ---")
	fmt.Printf("⚠️  即将清空Redis数据库 %d 中的所有数据\n", cfg.Redis.DB)
	fmt.Printf("⚠️  这将删除 %d 个键\n", dbSize)
	fmt.Println("⚠️  此操作不可逆！")
	
	// 自动确认（生产环境中可能需要手动确认）
	fmt.Println("\n--- 执行清空操作 ---")
	fmt.Println("🔄 开始清空Redis...")

	// 6. 执行清空操作
	startTime := time.Now()
	
	// 使用FLUSHDB命令清空当前数据库
	err = rdb.FlushDB(ctx).Err()
	if err != nil {
		log.Fatalf("❌ 清空Redis失败: %v", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ Redis清空完成，耗时: %v\n", duration)

	// 7. 验证清空结果
	fmt.Println("\n--- 验证清空结果 ---")
	newDbSize, err := rdb.DBSize(ctx).Result()
	if err != nil {
		log.Printf("❌ 验证失败: %v", err)
	} else {
		fmt.Printf("   清空后键数量: %d\n", newDbSize)
		if newDbSize == 0 {
			fmt.Println("✅ Redis清空验证成功")
		} else {
			fmt.Printf("⚠️  仍有 %d 个键未清空\n", newDbSize)
		}
	}

	// 8. 获取清空后的内存使用情况
	_, err = rdb.Info(ctx, "memory").Result()
	if err != nil {
		log.Printf("❌ 获取清空后内存信息失败: %v", err)
	} else {
		fmt.Printf("   内存信息更新成功\n")
	}

	fmt.Println("\n=== Redis清空完成 ===")
}
