package main

import (
	"fmt"
	"strings"
)

// isNumeric 检查字符串是否为纯数字
func isNumeric(s string) bool {
	if s == "" {
		return false
	}
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return true
}

// removeNumberPrefix 移除数字序号前缀
func removeNumberPrefix(text string) string {
	text = strings.TrimSpace(text)
	originalText := text
	
	fmt.Printf("🔍 [序号移除] 开始处理: %s\n", text)

	// 1. 处理中文顿号格式（如"1、"、"19、"）
	if strings.Contains(text, "、") {
		fmt.Printf("🔍 [序号移除] 发现中文顿号，开始处理\n")
		parts := strings.Split(text, "、")
		if len(parts) > 1 {
			// 检查第一部分是否为数字
			firstPart := strings.TrimSpace(parts[0])
			fmt.Printf("🔍 [序号移除] 顿号前部分: '%s'\n", firstPart)
			if isNumeric(firstPart) {
				result := strings.TrimSpace(strings.Join(parts[1:], "、"))
				fmt.Printf("✅ [序号移除] 中文顿号移除成功: %s -> %s\n", originalText, result)
				return result
			}
		}
	}

	// 2. 处理英文点号格式（如"1."）
	if strings.Contains(text, ".") {
		fmt.Printf("🔍 [序号移除] 发现英文点号，开始处理\n")
		parts := strings.Split(text, ".")
		if len(parts) > 1 {
			firstPart := strings.TrimSpace(parts[0])
			fmt.Printf("🔍 [序号移除] 点号前部分: '%s'\n", firstPart)
			if isNumeric(firstPart) {
				result := strings.TrimSpace(strings.Join(parts[1:], "."))
				fmt.Printf("✅ [序号移除] 英文点号移除成功: %s -> %s\n", originalText, result)
				return result
			}
		}
	}

	// 3. 处理括号格式（如"(1)"）
	if strings.HasPrefix(text, "(") {
		fmt.Printf("🔍 [序号移除] 发现括号格式，开始处理\n")
		for i := 1; i < len(text); i++ {
			if text[i] == ')' {
				// 检查括号内是否为数字
				numberPart := text[1:i]
				fmt.Printf("🔍 [序号移除] 括号内部分: '%s'\n", numberPart)
				if isNumeric(numberPart) {
					result := strings.TrimSpace(text[i+1:])
					fmt.Printf("✅ [序号移除] 括号格式移除成功: %s -> %s\n", originalText, result)
					return result
				}
				break
			}
		}
	}

	// 4. 处理纯数字开头的情况（如"19 题目内容"）
	for i := 0; i < len(text); i++ {
		char := text[i]
		if char >= '0' && char <= '9' {
			continue
		} else if char == ' ' || char == '\t' {
			// 找到空格，检查前面是否全是数字
			numberPart := text[:i]
			fmt.Printf("🔍 [序号移除] 空格前数字部分: '%s'\n", numberPart)
			if isNumeric(numberPart) {
				result := strings.TrimSpace(text[i:])
				fmt.Printf("✅ [序号移除] 数字空格格式移除成功: %s -> %s\n", originalText, result)
				return result
			}
			break
		} else {
			// 遇到非数字非空格字符，停止
			break
		}
	}

	fmt.Printf("ℹ️ [序号移除] 未发现序号格式，保持原文: %s\n", text)
	return text
}

func main() {
	fmt.Println("=== 序号移除逻辑测试 ===")
	
	testCases := []string{
		"1、于某驾驶一辆大型客车（核载46人,实载52人）在高速公路上以每小时110公里的速度行驶,追尾一辆重型货车,造成4人死亡6人受伤.于某存在的违法行为是什么？",
		"20、这是第二十题",
		"999、任意大数字",
		"5.选择题内容",
		"(15)括号格式题目",
		"25 空格分隔的题目",
		"abc、非数字不会被移除",
		"1a、混合字符不会被移除",
		"正常题目内容",
	}
	
	for i, testCase := range testCases {
		fmt.Printf("\n--- 测试用例 %d ---\n", i+1)
		fmt.Printf("输入: %s\n", testCase)
		result := removeNumberPrefix(testCase)
		fmt.Printf("输出: %s\n", result)
		fmt.Printf("是否移除: %t\n", testCase != result)
	}
}
